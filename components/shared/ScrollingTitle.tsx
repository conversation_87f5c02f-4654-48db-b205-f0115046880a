"use client"

import { useEffect, useRef, useState } from "react"

interface ScrollingTitleProps {
  children: string
  className?: string
  speed?: number // pixels per second
  pauseDuration?: number // milliseconds to pause at left edge (default 1500ms = 1.5s)
  gap?: number // gap between end and start of title in pixels (default 60px)
}

export default function ScrollingTitle({
  children,
  className = "",
  speed = 40, // human-friendly default speed (px/s)
  pauseDuration = 1500, // 1.5 second pause at left edge
  gap = 60 // 60px gap between end and start
}: ScrollingTitleProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const scrollerRef = useRef<HTMLDivElement>(null)
  const measureRef = useRef<HTMLDivElement>(null)

  const [isOverflowing, setIsOverflowing] = useState(false)

  // Animation state - 使用 useRef 避免重渲染
  const animationRef = useRef<{
    rafId: number | null
    cycleStartTime: number
    scrollDistance: number
    cycleDuration: number
    isActive: boolean
  }>({
    rafId: null,
    cycleStartTime: 0,
    scrollDistance: 0,
    cycleDuration: 0,
    isActive: false
  })

  // 清理函数 - 遵循最佳实践，确保动画正确停止
  const cleanup = () => {
    if (animationRef.current.rafId !== null) {
      cancelAnimationFrame(animationRef.current.rafId)
      animationRef.current.rafId = null
    }
    animationRef.current.isActive = false

    // 重置 transform 和 will-change 属性
    if (scrollerRef.current) {
      scrollerRef.current.style.transform = 'translateX(0px)'
      scrollerRef.current.style.willChange = 'auto'
    }
  }

  // 动画函数 - 优化性能，避免重复计算
  const animate = (currentTime: number) => {
    const el = scrollerRef.current
    if (!el || !animationRef.current.isActive) return

    const { cycleStartTime, cycleDuration, scrollDistance } = animationRef.current

    // 初始化循环开始时间
    if (cycleStartTime === 0) {
      animationRef.current.cycleStartTime = currentTime
      animationRef.current.rafId = requestAnimationFrame(animate)
      return
    }

    // 计算当前循环内的时间位置
    const timeInCycle = (currentTime - cycleStartTime) % cycleDuration

    if (timeInCycle < pauseDuration) {
      // 暂停阶段：标题开头在容器左边缘
      el.style.transform = 'translateX(0px)'
    } else {
      // 滚动阶段：从左边缘开始向左滚动
      const scrollTime = timeInCycle - pauseDuration
      const scrollDuration = cycleDuration - pauseDuration
      const scrollProgress = scrollTime / scrollDuration
      const currentOffset = scrollProgress * scrollDistance
      el.style.transform = `translateX(${-currentOffset}px)`
    }

    animationRef.current.rafId = requestAnimationFrame(animate)
  }

  useEffect(() => {
    const setup = () => {
      if (!containerRef.current || !measureRef.current || !scrollerRef.current) return

      const containerWidth = containerRef.current.offsetWidth
      const textWidth = measureRef.current.scrollWidth
      const overflowing = textWidth > containerWidth
      setIsOverflowing(overflowing)

      // 清理之前的动画
      cleanup()

      if (!overflowing) return

      // 计算动画参数
      const scrollDistance = textWidth + gap
      const scrollDuration = (scrollDistance / speed) * 1000 // 转换为毫秒
      const cycleDuration = pauseDuration + scrollDuration

      // 设置动画状态
      animationRef.current = {
        rafId: null,
        cycleStartTime: 0,
        scrollDistance,
        cycleDuration,
        isActive: true
      }

      // 设置性能优化属性
      scrollerRef.current.style.willChange = 'transform'

      // 开始动画
      animationRef.current.rafId = requestAnimationFrame(animate)
    }

    setup()
    window.addEventListener('resize', setup)
    return () => {
      window.removeEventListener('resize', setup)
      cleanup()
    }
  }, [children, speed, pauseDuration, gap])

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      style={{
        WebkitMaskImage: isOverflowing
          ? 'linear-gradient(to right, black 0%, black 80%, transparent 100%)'
          : 'none',
        maskImage: isOverflowing
          ? 'linear-gradient(to right, black 0%, black 80%, transparent 100%)'
          : 'none'
      }}
    >
      {/* Hidden element to measure original text width */}
      <div
        ref={measureRef}
        className="whitespace-nowrap absolute opacity-0 pointer-events-none"
        style={{ top: '-9999px' }}
      >
        {children}
      </div>

      <div
        ref={scrollerRef}
        className="whitespace-nowrap"
        style={{
          // 确保没有 CSS 动画冲突
          animation: 'none',
          // will-change 通过 JavaScript 动态设置以优化性能
        }}
      >
        {isOverflowing ? (
          // 重复内容以实现无缝循环
          // 只需要两个副本：当前显示的和下一个循环的
          <span>
            {children}
            <span style={{ display: 'inline-block', width: `${gap}px` }} />
            {children}
          </span>
        ) : (
          children
        )}
      </div>
    </div>
  )
}
