"use client"

import { useEffect, useRef, useState } from "react"

interface ScrollingTitleProps {
  children: string
  className?: string
  speed?: number // pixels per second
  pauseDuration?: number // milliseconds to pause at left edge (default 1500ms = 1.5s)
  gap?: number // gap between end and start of title in pixels (default 60px)
}

export default function ScrollingTitle({
  children,
  className = "",
  speed = 40, // human-friendly default speed (px/s)
  pauseDuration = 1500, // 1.5 second pause at left edge
  gap = 60 // 60px gap between end and start
}: ScrollingTitleProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const scrollerRef = useRef<HTMLDivElement>(null)
  const measureRef = useRef<HTMLDivElement>(null)

  const [isOverflowing, setIsOverflowing] = useState(false)

  // Animation state - 简化状态管理
  const animationRef = useRef<{
    rafId: number | null
    startTime: number
    scrollDistance: number
    scrollDuration: number
    isActive: boolean
    currentPhase: 'paused' | 'scrolling'
    phaseStartTime: number
  }>({
    rafId: null,
    startTime: 0,
    scrollDistance: 0,
    scrollDuration: 0,
    isActive: false,
    currentPhase: 'paused',
    phaseStartTime: 0
  })

  // 清理函数 - 遵循最佳实践，确保动画正确停止
  const cleanup = () => {
    if (animationRef.current.rafId !== null) {
      cancelAnimationFrame(animationRef.current.rafId)
      animationRef.current.rafId = null
    }
    animationRef.current.isActive = false

    // 重置 transform 和 will-change 属性
    if (scrollerRef.current) {
      scrollerRef.current.style.transform = 'translateX(0px)'
      scrollerRef.current.style.willChange = 'auto'
    }
  }

  // 全新的动画函数 - 基于状态机的简单实现
  const animate = (currentTime: number) => {
    const el = scrollerRef.current
    if (!el || !animationRef.current.isActive) return

    const { startTime, scrollDistance, scrollDuration, currentPhase, phaseStartTime } = animationRef.current

    // 初始化
    if (startTime === 0) {
      animationRef.current.startTime = currentTime
      animationRef.current.phaseStartTime = currentTime
      animationRef.current.currentPhase = 'paused'
      el.style.transform = 'translateX(0px)'
      // 初始化完成
      animationRef.current.rafId = requestAnimationFrame(animate)
      return
    }

    const phaseElapsed = currentTime - phaseStartTime

    if (currentPhase === 'paused') {
      // 暂停阶段
      el.style.transform = 'translateX(0px)'

      if (phaseElapsed >= pauseDuration) {
        // 切换到滚动阶段
        animationRef.current.currentPhase = 'scrolling'
        animationRef.current.phaseStartTime = currentTime
        console.log('ScrollingTitle: 切换到滚动阶段')
      }
    } else {
      // 滚动阶段
      const scrollProgress = Math.min(phaseElapsed / scrollDuration, 1)
      const currentOffset = scrollProgress * scrollDistance
      el.style.transform = `translateX(${-currentOffset}px)`

      if (phaseElapsed >= scrollDuration) {
        // 切换回暂停阶段
        animationRef.current.currentPhase = 'paused'
        animationRef.current.phaseStartTime = currentTime
        console.log('ScrollingTitle: 切换到暂停阶段')
      }
    }

    animationRef.current.rafId = requestAnimationFrame(animate)
  }

  useEffect(() => {
    const setup = () => {
      if (!containerRef.current || !measureRef.current || !scrollerRef.current) return

      const containerWidth = containerRef.current.offsetWidth
      const textWidth = measureRef.current.scrollWidth
      const overflowing = textWidth > containerWidth
      setIsOverflowing(overflowing)

      // 清理之前的动画
      cleanup()

      if (!overflowing) return

      // 计算动画参数
      const scrollDistance = textWidth + gap
      const scrollDuration = (scrollDistance / speed) * 1000 // 转换为毫秒

      // 设置动画状态
      animationRef.current = {
        rafId: null,
        startTime: 0,
        scrollDistance,
        scrollDuration,
        isActive: true,
        currentPhase: 'paused',
        phaseStartTime: 0
      }

      // 确保初始位置正确
      scrollerRef.current.style.transform = 'translateX(0px)'

      // 设置性能优化属性
      scrollerRef.current.style.willChange = 'transform'

      // 开始动画
      animationRef.current.rafId = requestAnimationFrame(animate)
    }

    setup()
    window.addEventListener('resize', setup)
    return () => {
      window.removeEventListener('resize', setup)
      cleanup()
    }
  }, [children, speed, pauseDuration, gap])

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      style={{
        WebkitMaskImage: isOverflowing
          ? 'linear-gradient(to right, black 0%, black 80%, transparent 100%)'
          : 'none',
        maskImage: isOverflowing
          ? 'linear-gradient(to right, black 0%, black 80%, transparent 100%)'
          : 'none'
      }}
    >
      {/* Hidden element to measure original text width */}
      <div
        ref={measureRef}
        className="whitespace-nowrap absolute opacity-0 pointer-events-none"
        style={{ top: '-9999px' }}
      >
        {children}
      </div>

      <div
        ref={scrollerRef}
        className="whitespace-nowrap"
        style={{
          // 确保没有 CSS 动画冲突
          animation: 'none',
          // will-change 通过 JavaScript 动态设置以优化性能
        }}
      >
        {isOverflowing ? (
          // 重复内容以实现无缝循环
          // 只需要两个副本：当前显示的和下一个循环的
          <span>
            {children}
            <span style={{ display: 'inline-block', width: `${gap}px` }} />
            {children}
          </span>
        ) : (
          children
        )}
      </div>
    </div>
  )
}
