"use client"

import { useEffect, useRef, useState } from "react"

interface ScrollingTitleProps {
  children: string
  className?: string
  speed?: number // pixels per second
  pauseDuration?: number // milliseconds to pause at left edge (default 1500ms = 1.5s)
  gap?: number // gap between end and start of title in pixels (default 60px)
}

export default function ScrollingTitle({
  children,
  className = "",
  speed = 40, // human-friendly default speed (px/s)
  pauseDuration = 1500, // 1.5 second pause at left edge
  gap = 60 // 60px gap between end and start
}: ScrollingTitleProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const scrollerRef = useRef<HTMLDivElement>(null)
  const measureRef = useRef<HTMLDivElement>(null)

  const [isOverflowing, setIsOverflowing] = useState(false)

  // Animation state
  const animationRef = useRef<{
    rafId: number | null
    startTime: number
    phase: 'paused' | 'scrolling'
    cycleStartTime: number
  }>({
    rafId: null,
    startTime: 0,
    phase: 'paused',
    cycleStartTime: 0
  })

  useEffect(() => {
    const cleanup = () => {
      if (animationRef.current.rafId !== null) {
        cancelAnimationFrame(animationRef.current.rafId)
        animationRef.current.rafId = null
      }
    }

    const setup = () => {
      if (!containerRef.current || !measureRef.current || !scrollerRef.current) return

      const containerWidth = containerRef.current.offsetWidth
      const textWidth = measureRef.current.scrollWidth
      const overflowing = textWidth > containerWidth
      setIsOverflowing(overflowing)

      // Reset transform and animation state
      scrollerRef.current.style.transform = `translateX(0px)`
      animationRef.current.cycleStartTime = 0
      cleanup()

      if (!overflowing) return

      // 算法核心：
      // 1. 一个完整循环 = 暂停时间 + 滚动时间
      // 2. 滚动距离 = 文本宽度 + 间隔
      // 3. 滚动时间 = 滚动距离 / 速度
      const scrollDistance = textWidth + gap
      const scrollDuration = (scrollDistance / speed) * 1000 // 转换为毫秒
      const cycleDuration = pauseDuration + scrollDuration

      const animate = (currentTime: number) => {
        const el = scrollerRef.current
        if (!el) return

        // 初始化循环开始时间
        if (animationRef.current.cycleStartTime === 0) {
          animationRef.current.cycleStartTime = currentTime
        }

        // 计算当前循环内的时间位置
        const timeInCycle = (currentTime - animationRef.current.cycleStartTime) % cycleDuration

        if (timeInCycle < pauseDuration) {
          // 暂停阶段：标题开头在容器左边缘
          animationRef.current.phase = 'paused'
          el.style.transform = `translateX(0px)`
        } else {
          // 滚动阶段：从左边缘开始向左滚动
          animationRef.current.phase = 'scrolling'
          const scrollTime = timeInCycle - pauseDuration
          const scrollProgress = scrollTime / scrollDuration
          const currentOffset = scrollProgress * scrollDistance
          el.style.transform = `translateX(${-currentOffset}px)`
        }

        animationRef.current.rafId = requestAnimationFrame(animate)
      }

      animationRef.current.rafId = requestAnimationFrame(animate)
    }

    setup()
    window.addEventListener('resize', setup)
    return () => {
      window.removeEventListener('resize', setup)
      cleanup()
    }
  }, [children, speed, pauseDuration, gap])

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      style={{
        WebkitMaskImage: isOverflowing
          ? 'linear-gradient(to right, black 0%, black 80%, transparent 100%)'
          : 'none',
        maskImage: isOverflowing
          ? 'linear-gradient(to right, black 0%, black 80%, transparent 100%)'
          : 'none'
      }}
    >
      {/* Hidden element to measure original text width */}
      <div
        ref={measureRef}
        className="whitespace-nowrap absolute opacity-0 pointer-events-none"
        style={{ top: '-9999px' }}
      >
        {children}
      </div>

      <div
        ref={scrollerRef}
        className="whitespace-nowrap will-change-transform"
      >
        {isOverflowing ? (
          // 重复内容以实现无缝循环
          // 只需要两个副本：当前显示的和下一个循环的
          <span>
            {children}
            <span style={{ display: 'inline-block', width: `${gap}px` }} />
            {children}
          </span>
        ) : (
          children
        )}
      </div>
    </div>
  )
}
