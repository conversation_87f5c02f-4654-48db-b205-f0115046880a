"use client"

import { useEffect, useRef, useState } from "react"

interface ScrollingTitleProps {
  children: string
  className?: string
  speed?: number // pixels per second
  pauseDuration?: number // milliseconds to pause at left edge (default 1000ms = 1s)
  gap?: number // gap between end and start of title in pixels (default 60px)
}

export default function ScrollingTitle({
  children,
  className = "",
  speed = 40, // human-friendly default speed (px/s)
  pauseDuration = 1500, // 1.5 second pause at left edge
  gap = 60 // 60px gap between end and start
}: ScrollingTitleProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const scrollerRef = useRef<HTMLDivElement>(null)
  const measureRef = useRef<HTMLDivElement>(null)

  const [isOverflowing, setIsOverflowing] = useState(false)

  // Animation refs
  const rafIdRef = useRef<number | null>(null)
  const lastTsRef = useRef<number>(0)

  // Start or restart animation when dependencies change
  useEffect(() => {
    const cleanup = () => {
      if (rafIdRef.current != null) {
        cancelAnimationFrame(rafIdRef.current)
        rafIdRef.current = null
      }
    }

    const setup = () => {
      if (!containerRef.current || !measureRef.current || !scrollerRef.current) return

      const containerWidth = containerRef.current.offsetWidth
      const textWidth = measureRef.current.scrollWidth
      const overflowing = textWidth > containerWidth
      setIsOverflowing(overflowing)

      // Debug: 检查溢出检测
      console.log('ScrollingTitle Debug:', {
        containerWidth,
        textWidth,
        overflowing,
        text: children
      })

      // Reset transform
      scrollerRef.current.style.transform = `translateX(0px)`

      cleanup()

      if (!overflowing) return

      // 简化：只需要知道一个标题+间隔的距离
      const stepDistance = textWidth + gap

      // 简单的状态：当前偏移量和是否暂停
      let offset = 0
      let isPaused = true
      let pauseUntil = performance.now() + pauseDuration

      const step = (ts: number) => {
        const el = scrollerRef.current
        if (!el) return

        if (isPaused) {
          // 检查暂停是否结束
          if (ts >= pauseUntil) {
            isPaused = false
            lastTsRef.current = ts
          }
        } else {
          // 滚动阶段：简单地增加偏移量
          const dt = ts - lastTsRef.current
          lastTsRef.current = ts
          offset += (speed * dt) / 1000

          // 检查是否需要暂停：每当偏移量是stepDistance的整数倍时
          if (offset >= stepDistance) {
            offset -= stepDistance  // 重置到下一个循环的开始
            isPaused = true
            pauseUntil = ts + pauseDuration
            console.log('ScrollingTitle: 到达下一个标题开头，暂停')
          }
        }

        // 应用变换
        el.style.transform = `translateX(${-offset}px)`

        rafIdRef.current = requestAnimationFrame(step)
      }

      rafIdRef.current = requestAnimationFrame(step)
    }

    setup()
    window.addEventListener('resize', setup)
    return () => {
      window.removeEventListener('resize', setup)
      cleanup()
    }
  }, [children, speed, pauseDuration, gap])

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      style={{
        WebkitMaskImage: isOverflowing
          ? 'linear-gradient(to right, black 0%, black 80%, transparent 100%)'
          : 'none',
        maskImage: isOverflowing
          ? 'linear-gradient(to right, black 0%, black 80%, transparent 100%)'
          : 'none'
      }}
    >
      {/* Hidden element to measure original text width */}
      <div
        ref={measureRef}
        className="whitespace-nowrap absolute opacity-0 pointer-events-none"
        style={{ top: '-9999px' }}
      >
        {children}
      </div>

      <div
        ref={scrollerRef}
        className="whitespace-nowrap will-change-transform"
      >
        {isOverflowing ? (
          // Repeat content for seamless loop
          <span>
            {children}
            <span style={{ display: 'inline-block', width: `${gap}px` }} />
            {children}
            <span style={{ display: 'inline-block', width: `${gap}px` }} />
            {children}
          </span>
        ) : (
          children
        )}
      </div>
    </div>
  )
}
