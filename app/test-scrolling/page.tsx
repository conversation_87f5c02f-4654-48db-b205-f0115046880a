"use client"

import ScrollingTitle from "@/components/shared/ScrollingTitle"

export default function TestScrollingPage() {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">ScrollingTitle 算法测试</h1>
      
      <div className="space-y-6">
        {/* 短标题 - 不应该滚动 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">短标题（不滚动）</h2>
          <div className="w-96 border border-gray-300 p-4 bg-gray-50">
            <ScrollingTitle className="text-sm font-medium">
              短标题
            </ScrollingTitle>
          </div>
        </div>

        {/* 中等长度标题 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">中等长度标题</h2>
          <div className="w-96 border border-gray-300 p-4 bg-gray-50">
            <ScrollingTitle className="text-sm font-medium">
              VISA Service Performance Monitor - 中等长度的标题测试
            </ScrollingTitle>
          </div>
        </div>

        {/* 长标题 - 应该滚动 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">长标题（应该滚动）</h2>
          <div className="w-96 border border-gray-300 p-4 bg-gray-50">
            <ScrollingTitle className="text-sm font-medium">
              VISA Service Performance Monitor - 这是一个非常长的标题，应该会触发滚动动画效果
            </ScrollingTitle>
          </div>
        </div>

        {/* 超长标题 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">超长标题</h2>
          <div className="w-96 border border-gray-300 p-4 bg-gray-50">
            <ScrollingTitle className="text-sm font-medium">
              VISA Service Performance Monitor - 这是一个超级长的标题，用来测试滚动算法的效果，包含了很多文字内容来验证暂停和滚动的时机是否正确
            </ScrollingTitle>
          </div>
        </div>

        {/* 不同速度测试 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">不同速度测试</h2>
          <div className="space-y-4">
            <div className="w-96 border border-gray-300 p-4 bg-gray-50">
              <p className="text-xs text-gray-600 mb-2">慢速 (20 px/s)</p>
              <ScrollingTitle className="text-sm font-medium" speed={20}>
                VISA Service Performance Monitor - 慢速滚动测试
              </ScrollingTitle>
            </div>
            <div className="w-96 border border-gray-300 p-4 bg-gray-50">
              <p className="text-xs text-gray-600 mb-2">默认速度 (40 px/s)</p>
              <ScrollingTitle className="text-sm font-medium">
                VISA Service Performance Monitor - 默认速度滚动测试
              </ScrollingTitle>
            </div>
            <div className="w-96 border border-gray-300 p-4 bg-gray-50">
              <p className="text-xs text-gray-600 mb-2">快速 (80 px/s)</p>
              <ScrollingTitle className="text-sm font-medium" speed={80}>
                VISA Service Performance Monitor - 快速滚动测试
              </ScrollingTitle>
            </div>
          </div>
        </div>

        {/* 不同暂停时间测试 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">不同暂停时间测试</h2>
          <div className="space-y-4">
            <div className="w-96 border border-gray-300 p-4 bg-gray-50">
              <p className="text-xs text-gray-600 mb-2">短暂停 (500ms)</p>
              <ScrollingTitle className="text-sm font-medium" pauseDuration={500}>
                VISA Service Performance Monitor - 短暂停测试
              </ScrollingTitle>
            </div>
            <div className="w-96 border border-gray-300 p-4 bg-gray-50">
              <p className="text-xs text-gray-600 mb-2">默认暂停 (1500ms)</p>
              <ScrollingTitle className="text-sm font-medium">
                VISA Service Performance Monitor - 默认暂停测试
              </ScrollingTitle>
            </div>
            <div className="w-96 border border-gray-300 p-4 bg-gray-50">
              <p className="text-xs text-gray-600 mb-2">长暂停 (3000ms)</p>
              <ScrollingTitle className="text-sm font-medium" pauseDuration={3000}>
                VISA Service Performance Monitor - 长暂停测试
              </ScrollingTitle>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded">
        <h3 className="font-semibold text-blue-800 mb-2">测试要点</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 短标题不应该滚动</li>
          <li>• 长标题应该在开头到达左边缘时暂停 1.5 秒</li>
          <li>• 滚动应该流畅，没有跳跃</li>
          <li>• 循环应该无缝衔接</li>
          <li>• 不同速度和暂停时间应该按预期工作</li>
        </ul>
      </div>
    </div>
  )
}
