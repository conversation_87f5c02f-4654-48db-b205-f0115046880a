"use client"

import ScrollingTitle from "@/components/shared/ScrollingTitle"

export default function DebugScrollingPage() {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">ScrollingTitle 调试页面</h1>
      
      <div className="space-y-6">
        {/* 单个测试用例 - 便于调试 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">调试测试 - 长标题</h2>
          <div className="w-96 border border-red-300 p-4 bg-red-50">
            <ScrollingTitle 
              className="text-sm font-medium"
              speed={40}
              pauseDuration={2000}
              gap={60}
            >
              VISA Service Performance Monitor - 这是一个用于调试的长标题
            </ScrollingTitle>
          </div>
        </div>

        {/* 简化的测试用例 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">简化测试</h2>
          <div className="w-64 border border-blue-300 p-4 bg-blue-50">
            <ScrollingTitle 
              className="text-sm font-medium"
              speed={20}
              pauseDuration={1000}
              gap={30}
            >
              这是一个简化的测试标题
            </ScrollingTitle>
          </div>
        </div>
      </div>

      <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h3 className="font-semibold text-yellow-800 mb-2">调试说明</h3>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• 打开浏览器开发者工具查看控制台输出</li>
          <li>• 观察动画参数和时间计算</li>
          <li>• 检查是否有跳跃或回跳现象</li>
          <li>• 验证暂停时机是否正确</li>
        </ul>
      </div>
    </div>
  )
}
